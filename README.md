# Flask App

A simple Flask application.

## Setup

1. Create a virtual environment:

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. Run the app:

   ```bash
   python run.py
   ```

## Structure

- `app/` - Application package
  - `templates/` - HTML templates
  - `static/` - Static files (CSS, JS, images)
  - `__init__.py` - App factory
  - `routes.py` - Route definitions
- `run.py` - Entry point
- `requirements.txt` - Python dependencies
- `.gitignore` - Git ignore rules
