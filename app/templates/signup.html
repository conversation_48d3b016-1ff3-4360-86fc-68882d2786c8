<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sign Up</title>
</head>
<body>
    <h2>Sign Up</h2>
    {% if error %}
    <div style="color: red; margin-bottom: 10px;">{{ error }}</div>
    {% endif %}
    <a href="/login/google"><button type="button">Sign up with Google</button></a>
    <hr>
    <form method="POST" action="/signup">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" value="{{ username|default('') }}" required><br>
        
        <label for="family_name">family_name:</label>
        <input type="text" id="family_name" name="family_name" value="{{ family_name|default('') }}" required><br>
        
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" value="{{ email|default('') }}" required><br>

        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required><br>

        <label for="confirm_password">Confirm Password:</label>
        <input type="password" id="confirm_password" name="confirm_password" required><br>

        <label for="dob">Date of Birth:</label>
        <input type="date" id="dob" name="dob" value="{{ dob|default('') }}"><br>

        <label for="class">Class:</label>
        <select id="class" name="class" required onchange="window.location='?class='+this.value{% if selected_subject %}+'&subject={{ selected_subject }}'{% endif %}"> 
            <option value="">Select your class</option>
            <option value="1" {% if selected_class == '1' %}selected{% endif %}>1er</option>
            <option value="2" {% if selected_class == '2' %}selected{% endif %}>2éme</option>
            <option value="3" {% if selected_class == '3' %}selected{% endif %}>3éme</option>
            <option value="4" {% if selected_class == '4' %}selected{% endif %}>bacalaureat</option>
        </select><br>

        {% if selected_class in ['2', '3', '4'] %}
        <label for="subject">Subject:</label>
        <select id="subject" name="subject">
            <option value="">Select subject</option>
            <option value="Math" {% if selected_subject == 'Math' %}selected{% endif %}>Math</option>
            <option value="Science" {% if selected_subject == 'Science' %}selected{% endif %}>Science</option>
            <option value="Informatique" {% if selected_subject == 'Informatique' %}selected{% endif %}>Informatique</option>
            {% if selected_class != '2' %}
            <option value="Technique" {% if selected_subject == 'Technique' %}selected{% endif %}>Technique</option>
            {% endif %}
        </select><br>
        {% endif %}

        <button type="submit">Sign Up</button>
    </form>
    <p>Already have an account? <a href="/login">Login</a></p>
</body>
</html>
