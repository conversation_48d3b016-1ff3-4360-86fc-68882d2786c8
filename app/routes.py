
from flask import render_template, request, redirect, url_for
from flask_dance.contrib.google import make_google_blueprint, google
import os



def register_routes(app):
    # Setup Google OAuth blueprint
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'supersecret')
    google_bp = make_google_blueprint(
        client_id=os.environ.get('GOOGLE_OAUTH_CLIENT_ID', '1058768724753-1hoshmnb1pm30uram32amr1k910rl41p.apps.googleusercontent.com'),
        client_secret=os.environ.get('GOOGLE_OAUTH_CLIENT_SECRET', '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'),
        redirect_to='google_login_callback',
        scope=["profile", "email"]
    )
    app.register_blueprint(google_bp, url_prefix="/login")

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            # Handle login logic here
            return redirect(url_for('home'))
        return render_template('login.html')

    @app.route('/login/google')
    def login_google():
        if not google.authorized:
            return redirect(url_for('google.login'))
        resp = google.get("/oauth2/v2/userinfo")
        assert resp.ok, resp.text
        user_info = resp.json()
        # Here, you would create or authenticate the user in your DB
        # For demo, just redirect to home
        return redirect(url_for('home'))

    @app.route('/google_login_callback')
    def google_login_callback():
        # This route is used as the redirect after Google login
        if not google.authorized:
            return redirect(url_for('login'))
        resp = google.get("/oauth2/v2/userinfo")
        assert resp.ok, resp.text
        user_info = resp.json()
        # Here, you would create or authenticate the user in your DB
        # For demo, just redirect to home
        return redirect(url_for('home'))

    @app.route('/signup', methods=['GET', 'POST'])
    def signup():
        error = None
        if request.method == 'POST':
            username = request.form.get('username', '').strip()
            familyname = request.form.get('family_name', '').strip()
            email = request.form.get('email', '').strip()
            password = request.form.get('password', '')
            confirm_password = request.form.get('confirm_password', '')
            dob = request.form.get('dob', '').strip()
            selected_class = request.form.get('class', '')
            selected_subject = request.form.get('subject', '')

            # Validation
            if not username:
                error = 'Username is required.'
            elif not email or '@' not in email:
                error = 'Valid email is required.'
            elif not password or not confirm_password:
                error = 'Password and confirmation are required.'
            elif password != confirm_password:
                error = 'Passwords do not match.'
            elif not selected_class:
                error = 'Class is required.'
            elif selected_class in ['2', '3', '4'] and not selected_subject:
                error = 'Subject is required for this class.'
            # Add more checks as needed

            if not error:
                # Handle signup logic here (e.g., save user)
                return redirect(url_for('login'))
        else:
            selected_class = request.args.get('class', '')
            selected_subject = request.args.get('subject', '')
            username = email = password = confirm_password = familyname = dob = ''
            error = None
        return render_template(
            'signup.html',
            selected_class=selected_class,
            selected_subject=selected_subject,
            username=username,
            email=email,
            familyname=familyname,
            dob=dob,
            error=error
        )
